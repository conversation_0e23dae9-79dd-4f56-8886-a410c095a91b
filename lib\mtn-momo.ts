/**
 * MTN MoMo Collections API Service
 * Handles authentication and request-to-pay functionality
 */

import { v4 as uuidv4 } from 'uuid'

export interface MoMoConfig {
  subscriptionKey: string
  environment: 'sandbox' | 'production'
  baseUrl?: string
}

export interface MoMoUser {
  userId: string
  apiKey: string
}

export interface MoMoToken {
  accessToken: string
  tokenType: string
  expiresIn: number
  expiresAt: Date
}

export interface RequestToPayRequest {
  amount: string
  currency: string
  externalId: string
  payer: {
    partyIdType: 'MSISDN'
    partyId: string // Phone number
  }
  payerMessage?: string
  payeeNote?: string
}

export interface RequestToPayResponse {
  financialTransactionId?: string
  externalId: string
  amount: string
  currency: string
  payer: {
    partyIdType: string
    partyId: string
  }
  payerMessage?: string
  payeeNote?: string
  status: 'PENDING' | 'SUCCESSFUL' | 'FAILED'
}

export class MoMoError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number
  ) {
    super(message)
    this.name = 'MoMoError'
  }
}

export class MTNMoMoService {
  private config: MoMoConfig
  private baseUrl: string
  private tokenCache: Map<string, MoMoToken> = new Map()

  constructor(config: MoMoConfig) {
    this.config = config
    // Use the correct MTN MoMo sandbox base URL
    this.baseUrl = config.baseUrl || 'https://sandbox.momodeveloper.mtn.com'

    // Disable SSL verification for sandbox environment (development only)
    if (this.config.environment === 'sandbox') {
      process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'
      console.log('🔓 SSL verification disabled for sandbox environment')
    }

    console.log('MTN MoMo Service initialized with baseUrl:', this.baseUrl)
  }

  /**
   * Step 1: Create API User
   */
  async createApiUser(callbackHost?: string): Promise<string> {
    const userId = uuidv4()
    
    const response = await fetch(`${this.baseUrl}/v1_0/apiuser`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Reference-Id': userId,
        'Ocp-Apim-Subscription-Key': this.config.subscriptionKey,
      },
      body: JSON.stringify({
        providerCallbackHost: callbackHost || 'https://webhook.site/unique-id'
      }),
      signal: AbortSignal.timeout(10000) // 10 second timeout
    })

    if (!response.ok) {
      const error = await response.text()
      throw new MoMoError(
        `Failed to create API user: ${error}`,
        'CREATE_USER_FAILED',
        response.status
      )
    }

    return userId
  }

  /**
   * Step 2: Create API Key
   */
  async createApiKey(userId: string): Promise<string> {
    const response = await fetch(`${this.baseUrl}/v1_0/apiuser/${userId}/apikey`, {
      method: 'POST',
      headers: {
        'Ocp-Apim-Subscription-Key': this.config.subscriptionKey,
      },
      signal: AbortSignal.timeout(10000) // 10 second timeout
    })

    if (!response.ok) {
      const error = await response.text()
      throw new MoMoError(
        `Failed to create API key: ${error}`,
        'CREATE_KEY_FAILED',
        response.status
      )
    }

    const data = await response.json()
    return data.apiKey
  }

  /**
   * Step 3: Create Bearer Token
   */
  async createToken(userId: string, apiKey: string): Promise<MoMoToken> {
    // Check cache first
    const cacheKey = `${userId}:${apiKey}`
    const cached = this.tokenCache.get(cacheKey)
    if (cached && cached.expiresAt > new Date()) {
      return cached
    }

    const credentials = Buffer.from(`${userId}:${apiKey}`).toString('base64')
    
    console.log('Creating token with URL:', `${this.baseUrl}/collection/token`)
    console.log('Environment:', this.config.environment)
    console.log('Subscription Key (first 10 chars):', this.config.subscriptionKey?.substring(0, 10) + '...')

    const response = await fetch(`${this.baseUrl}/collection/token`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${credentials}`,
        'Ocp-Apim-Subscription-Key': this.config.subscriptionKey,
        'X-Target-Environment': this.config.environment,
      },
      // Increase timeout and add better error handling
      signal: AbortSignal.timeout(30000) // 30 second timeout
    })

    if (!response.ok) {
      const error = await response.text()
      console.error('❌ Token creation failed:')
      console.error('Status:', response.status)
      console.error('Response:', error)
      console.error('Headers:', Object.fromEntries(response.headers.entries()))
      throw new MoMoError(
        `Failed to create token: ${error}`,
        'CREATE_TOKEN_FAILED',
        response.status
      )
    }

    const data = await response.json()
    const token: MoMoToken = {
      accessToken: data.access_token,
      tokenType: data.token_type,
      expiresIn: data.expires_in,
      expiresAt: new Date(Date.now() + (data.expires_in * 1000))
    }

    // Cache the token
    this.tokenCache.set(cacheKey, token)
    return token
  }

  /**
   * Step 4: Request to Pay
   */
  async requestToPay(
    request: RequestToPayRequest,
    momoUser: MoMoUser
  ): Promise<string> {
    const token = await this.createToken(momoUser.userId, momoUser.apiKey)
    const transactionId = uuidv4()

    const response = await fetch(`${this.baseUrl}/collection/v1_0/requesttopay`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token.accessToken}`,
        'X-Reference-Id': transactionId,
        'X-Target-Environment': this.config.environment,
        'Ocp-Apim-Subscription-Key': this.config.subscriptionKey,
      },
      body: JSON.stringify(request),
      signal: AbortSignal.timeout(15000) // 15 second timeout for payment requests
    })

    if (!response.ok) {
      const error = await response.text()
      throw new MoMoError(
        `Request to pay failed: ${error}`,
        'REQUEST_TO_PAY_FAILED',
        response.status
      )
    }

    return transactionId
  }

  /**
   * Step 5: Get Transaction Status
   */
  async getTransactionStatus(
    transactionId: string,
    momoUser: MoMoUser
  ): Promise<RequestToPayResponse> {
    const token = await this.createToken(momoUser.userId, momoUser.apiKey)

    const response = await fetch(
      `${this.baseUrl}/collection/v1_0/requesttopay/${transactionId}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token.accessToken}`,
          'X-Target-Environment': this.config.environment,
          'Ocp-Apim-Subscription-Key': this.config.subscriptionKey,
        },
        signal: AbortSignal.timeout(10000) // 10 second timeout
      }
    )

    if (!response.ok) {
      const error = await response.text()
      throw new MoMoError(
        `Failed to get transaction status: ${error}`,
        'GET_STATUS_FAILED',
        response.status
      )
    }

    return await response.json()
  }

  /**
   * Complete flow: Create user and key (for initial setup)
   */
  async setupApiUser(callbackHost?: string): Promise<MoMoUser> {
    const userId = await this.createApiUser(callbackHost)
    const apiKey = await this.createApiKey(userId)
    
    return { userId, apiKey }
  }

  /**
   * Clear token cache (useful for testing)
   */
  clearTokenCache(): void {
    this.tokenCache.clear()
  }
}
