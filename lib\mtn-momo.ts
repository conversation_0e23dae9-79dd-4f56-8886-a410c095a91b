/**
 * MTN MoMo Collections API Service
 * Handles authentication and request-to-pay functionality
 */

import { v4 as uuidv4 } from 'uuid'

export interface MoMoConfig {
  subscriptionKey: string
  environment: 'sandbox' | 'production'
  baseUrl?: string
}

export interface MoMoUser {
  userId: string
  apiKey: string
}

export interface MoMoToken {
  accessToken: string
  tokenType: string
  expiresIn: number
  expiresAt: Date
}

export interface RequestToPayRequest {
  amount: string
  currency: string
  externalId: string
  payer: {
    partyIdType: 'MSISDN'
    partyId: string // Phone number
  }
  payerMessage?: string
  payeeNote?: string
}

export interface RequestToPayResponse {
  financialTransactionId?: string
  externalId: string
  amount: string
  currency: string
  payer: {
    partyIdType: string
    partyId: string
  }
  payerMessage?: string
  payeeNote?: string
  status: 'PENDING' | 'SUCCESSFUL' | 'FAILED'
}

export class MoMoError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number
  ) {
    super(message)
    this.name = 'MoMoError'
  }
}

export class MTNMoMoService {
  private config: MoMoConfig
  private baseUrl: string
  private tokenCache: Map<string, MoMoToken> = new Map()

  constructor(config: MoMoConfig) {
    this.config = config
    // Use the correct MTN MoMo sandbox base URL
    this.baseUrl = config.baseUrl || 'https://sandbox.momodeveloper.mtn.com'

    // Disable SSL verification for sandbox environment (development only)
    if (this.config.environment === 'sandbox') {
      process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0'
      console.log('🔓 SSL verification disabled for sandbox environment')
    }

    console.log('MTN MoMo Service initialized with baseUrl:', this.baseUrl)
  }

  /**
   * Step 1: Create API User
   * Based on MTN MoMo official documentation
   */
  async createApiUser(callbackHost?: string): Promise<string> {
    const userId = uuidv4()

    console.log('🔧 Creating MTN MoMo API User...')
    console.log('User ID:', userId)
    console.log('Callback Host:', callbackHost || 'https://webhook.site/unique-id')

    const response = await fetch(`${this.baseUrl}/v1_0/apiuser`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Reference-Id': userId,
        'Ocp-Apim-Subscription-Key': this.config.subscriptionKey,
      },
      body: JSON.stringify({
        providerCallbackHost: callbackHost || 'https://webhook.site/unique-id'
      }),
      signal: AbortSignal.timeout(30000) // 30 second timeout
    })

    console.log('API User creation response status:', response.status)
    console.log('Response headers:', Object.fromEntries(response.headers.entries()))

    if (!response.ok) {
      const error = await response.text()
      console.error('❌ API User creation failed:')
      console.error('Status:', response.status)
      console.error('Error response:', error)
      throw new MoMoError(
        `Failed to create API user: ${error}`,
        'CREATE_USER_FAILED',
        response.status
      )
    }

    console.log('✅ API User created successfully with ID:', userId)
    return userId
  }

  /**
   * Step 2: Create API Key
   * Based on MTN MoMo official documentation
   */
  async createApiKey(userId: string): Promise<string> {
    console.log('🔑 Creating API Key for User ID:', userId)

    // Add retry logic for network timeouts
    let lastError: Error | null = null
    for (let attempt = 1; attempt <= 3; attempt++) {
      try {
        console.log(`Attempt ${attempt}/3 to create API Key...`)

        const response = await fetch(`${this.baseUrl}/v1_0/apiuser/${userId}/apikey`, {
          method: 'POST',
          headers: {
            'Ocp-Apim-Subscription-Key': this.config.subscriptionKey,
          },
          signal: AbortSignal.timeout(45000) // 45 second timeout
        })

        console.log('API Key creation response status:', response.status)
        console.log('Response headers:', Object.fromEntries(response.headers.entries()))

        if (!response.ok) {
          const error = await response.text()
          console.error('❌ API Key creation failed:')
          console.error('Status:', response.status)
          console.error('Error response:', error)
          throw new MoMoError(
            `Failed to create API key: ${error}`,
            'CREATE_KEY_FAILED',
            response.status
          )
        }

        const data = await response.json()
        console.log('✅ API Key created successfully')
        console.log('API Key (first 10 chars):', data.apiKey?.substring(0, 10) + '...')
        return data.apiKey

      } catch (error) {
        lastError = error as Error
        console.error(`❌ Attempt ${attempt}/3 failed:`, error.message)

        if (attempt < 3) {
          const delay = attempt * 2000 // 2s, 4s delay between retries
          console.log(`⏳ Waiting ${delay}ms before retry...`)
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }

    // All attempts failed
    throw new MoMoError(
      `Failed to create API key after 3 attempts: ${lastError?.message}`,
      'CREATE_KEY_FAILED',
      500
    )
  }

  /**
   * Step 3: Create Bearer Token
   * Based on MTN MoMo official documentation
   */
  async createToken(userId: string, apiKey: string): Promise<MoMoToken> {
    // Validate inputs
    if (!userId || !apiKey) {
      throw new MoMoError(
        'Invalid credentials: userId and apiKey are required',
        'INVALID_CREDENTIALS',
        400
      )
    }

    // Check cache first
    const cacheKey = `${userId}:${apiKey}`
    const cached = this.tokenCache.get(cacheKey)
    if (cached && cached.expiresAt > new Date()) {
      console.log('✅ Using cached token')
      return cached
    }

    console.log('🔑 Creating Bearer Token...')
    console.log('User ID (first 10 chars):', userId.substring(0, 10) + '...')
    console.log('API Key (first 10 chars):', apiKey.substring(0, 10) + '...')

    const credentials = Buffer.from(`${userId}:${apiKey}`).toString('base64')

    console.log('Token URL:', `${this.baseUrl}/collection/token/`)
    console.log('Environment:', this.config.environment)
    console.log('Subscription Key (first 10 chars):', this.config.subscriptionKey?.substring(0, 10) + '...')

    const response = await fetch(`${this.baseUrl}/collection/token/`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${credentials}`,
        'Ocp-Apim-Subscription-Key': this.config.subscriptionKey,
        'X-Target-Environment': this.config.environment,
      },
      signal: AbortSignal.timeout(30000) // 30 second timeout
    })

    console.log('Token creation response status:', response.status)
    console.log('Response headers:', Object.fromEntries(response.headers.entries()))

    if (!response.ok) {
      const error = await response.text()
      console.error('❌ Token creation failed:')
      console.error('Status:', response.status)
      console.error('Response:', error)
      console.error('This usually means:')
      console.error('1. Invalid User ID or API Key (credentials not created properly)')
      console.error('2. API User creation failed earlier')
      console.error('3. Subscription key lacks permissions')
      throw new MoMoError(
        `Failed to create token: ${error}`,
        'CREATE_TOKEN_FAILED',
        response.status
      )
    }

    const data = await response.json()
    console.log('✅ Bearer Token created successfully')
    console.log('Token type:', data.token_type)
    console.log('Expires in:', data.expires_in, 'seconds')

    const token: MoMoToken = {
      accessToken: data.access_token,
      tokenType: data.token_type,
      expiresIn: data.expires_in,
      expiresAt: new Date(Date.now() + (data.expires_in * 1000))
    }

    // Cache the token
    this.tokenCache.set(cacheKey, token)
    return token
  }

  /**
   * Step 4: Request to Pay
   */
  async requestToPay(
    request: RequestToPayRequest,
    momoUser: MoMoUser
  ): Promise<string> {
    const token = await this.createToken(momoUser.userId, momoUser.apiKey)
    const transactionId = uuidv4()

    const response = await fetch(`${this.baseUrl}/collection/v1_0/requesttopay`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token.accessToken}`,
        'X-Reference-Id': transactionId,
        'X-Target-Environment': this.config.environment,
        'Ocp-Apim-Subscription-Key': this.config.subscriptionKey,
      },
      body: JSON.stringify(request),
      signal: AbortSignal.timeout(15000) // 15 second timeout for payment requests
    })

    if (!response.ok) {
      const error = await response.text()
      throw new MoMoError(
        `Request to pay failed: ${error}`,
        'REQUEST_TO_PAY_FAILED',
        response.status
      )
    }

    return transactionId
  }

  /**
   * Step 5: Get Transaction Status
   */
  async getTransactionStatus(
    transactionId: string,
    momoUser: MoMoUser
  ): Promise<RequestToPayResponse> {
    const token = await this.createToken(momoUser.userId, momoUser.apiKey)

    const response = await fetch(
      `${this.baseUrl}/collection/v1_0/requesttopay/${transactionId}`,
      {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token.accessToken}`,
          'X-Target-Environment': this.config.environment,
          'Ocp-Apim-Subscription-Key': this.config.subscriptionKey,
        },
        signal: AbortSignal.timeout(10000) // 10 second timeout
      }
    )

    if (!response.ok) {
      const error = await response.text()
      throw new MoMoError(
        `Failed to get transaction status: ${error}`,
        'GET_STATUS_FAILED',
        response.status
      )
    }

    return await response.json()
  }

  /**
   * Complete flow: Create user and key (for initial setup)
   * Based on MTN MoMo official documentation
   */
  async setupApiUser(callbackHost?: string): Promise<MoMoUser> {
    console.log('🚀 Starting MTN MoMo API User setup...')

    try {
      // Step 1: Create API User
      const userId = await this.createApiUser(callbackHost)

      // Step 2: Create API Key
      const apiKey = await this.createApiKey(userId)

      console.log('✅ MTN MoMo API User setup completed successfully')
      return { userId, apiKey }

    } catch (error) {
      console.error('❌ MTN MoMo API User setup failed:', error)
      throw error
    }
  }

  /**
   * Clear token cache (useful for testing)
   */
  clearTokenCache(): void {
    this.tokenCache.clear()
  }
}
