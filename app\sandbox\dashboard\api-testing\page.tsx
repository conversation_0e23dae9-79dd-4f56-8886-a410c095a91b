"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Copy, Check, RefreshCw, Zap, AlertTriangle, Clock, Server, ArrowRight } from "lucide-react"

// Request to pay form schema
const requestToPaySchema = z.object({
  phoneNumber: z.string()
    .min(9, "Phone number must be at least 9 digits")
    .regex(/^\+?[0-9]+$/, "Phone number must contain only numbers and an optional + prefix"),
  amount: z.string()
    .min(1, "Amount is required")
    .regex(/^[0-9]+$/, "Amount must be a number")
    .refine(val => parseInt(val) >= 100, "Amount must be at least 100 XAF"),
  externalId: z.string().min(1, "External ID is required"),
  description: z.string().min(3, "Description must be at least 3 characters"),
})

// Status check form schema
const statusCheckSchema = z.object({
  transactionId: z.string().min(1, "Transaction ID is required"),
})

// Mock API response types
type TransactionStatus = "SUCCESSFUL" | "FAILED" | "PENDING" | "TIMEOUT"

interface ApiResponse {
  status: TransactionStatus
  transactionId: string
  externalId: string
  amount: string
  currency: string
  payerParty: {
    partyIdType: string
    partyId: string
  }
  payeeParty: {
    partyIdType: string
    partyId: string
  }
  reason?: {
    code: string
    message: string
  }
  requestTimestamp: string
  responseTimestamp: string | null
}

export default function ApiTestingPage() {
  const [activeTab, setActiveTab] = useState("request-to-pay")
  const [apiResponse, setApiResponse] = useState<ApiResponse | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [copied, setCopied] = useState(false)
  const [testScenario, setTestScenario] = useState<"success" | "failure" | "pending" | "timeout" | null>(null)

  // Request to pay form
  const requestToPayForm = useForm<z.infer<typeof requestToPaySchema>>({
    resolver: zodResolver(requestToPaySchema),
    defaultValues: {
      phoneNumber: "+************",
      amount: "1000",
      externalId: 'ext_test_sample_id',
      description: "Test payment request",
    },
  })

  // Status check form
  const statusCheckForm = useForm<z.infer<typeof statusCheckSchema>>({
    resolver: zodResolver(statusCheckSchema),
    defaultValues: {
      transactionId: "",
    },
  })

  // Generate new external ID
  const generateExternalId = () => {
    // Use client-side only code for random generation
    if (typeof window !== 'undefined') {
      const randomId = 'ext_' + Math.random().toString(36).substring(2, 10) + '_' + Date.now().toString(36)
      requestToPayForm.setValue("externalId", randomId)
    } else {
      requestToPayForm.setValue("externalId", 'ext_test_sample_id')
    }
  }

  // Copy transaction ID to status check
  const copyTransactionIdToStatusCheck = () => {
    if (apiResponse?.transactionId) {
      statusCheckForm.setValue("transactionId", apiResponse.transactionId)
      setActiveTab("status-check")
    }
  }

  // Copy to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  // Handle form submission for request to pay
  const onRequestToPaySubmit = (values: z.infer<typeof requestToPaySchema>) => {
    setIsLoading(true)
    setApiResponse(null)

    // Simulate API call delay
    setTimeout(() => {
      // Generate mock response based on selected test scenario or default to success
      const responseStatus = testScenario || "success"
      
      const mockResponse: ApiResponse = {
        status: 
          responseStatus === "success" ? "SUCCESSFUL" :
          responseStatus === "failure" ? "FAILED" :
          responseStatus === "pending" ? "PENDING" : "TIMEOUT",
        transactionId: `txn_test_${responseStatus}_${new Date().toISOString().replace(/[-:.TZ]/g, '')}`,
        externalId: values.externalId,
        amount: values.amount,
        currency: "XAF",
        payerParty: {
          partyIdType: "MSISDN",
          partyId: values.phoneNumber,
        },
        payeeParty: {
          partyIdType: "MSISDN",
          partyId: "+************",
        },
        reason: 
          responseStatus === "success" ? {
            code: "PAYER_COMPLETED",
            message: "The payer has completed the payment"
          } :
          responseStatus === "failure" ? {
            code: "PAYER_NOT_FOUND",
            message: "The payer account was not found"
          } :
          responseStatus === "pending" ? {
            code: "AWAITING_PAYER_CONFIRMATION",
            message: "Waiting for the payer to confirm the payment"
          } : {
            code: "TRANSACTION_TIMEOUT",
            message: "The transaction timed out"
          },
        requestTimestamp: new Date().toISOString(),
        responseTimestamp: responseStatus === "pending" ? null : new Date().toISOString(),
      }

      setApiResponse(mockResponse)
      setIsLoading(false)
      setTestScenario(null)
    }, 1500)
  }

  // Handle form submission for status check
  const onStatusCheckSubmit = (values: z.infer<typeof statusCheckSchema>) => {
    setIsLoading(true)
    setApiResponse(null)

    // Simulate API call delay
    setTimeout(() => {
      // Generate mock response based on selected test scenario or default to success
      const responseStatus = testScenario || "success"
      
      const mockResponse: ApiResponse = {
        status: 
          responseStatus === "success" ? "SUCCESSFUL" :
          responseStatus === "failure" ? "FAILED" :
          responseStatus === "pending" ? "PENDING" : "TIMEOUT",
        transactionId: values.transactionId,
        externalId: `ext_${Math.random().toString(36).substring(2, 10)}`,
        amount: "1000",
        currency: "XAF",
        payerParty: {
          partyIdType: "MSISDN",
          partyId: "+************",
        },
        payeeParty: {
          partyIdType: "MSISDN",
          partyId: "+************",
        },
        reason: 
          responseStatus === "success" ? {
            code: "PAYER_COMPLETED",
            message: "The payer has completed the payment"
          } :
          responseStatus === "failure" ? {
            code: "PAYER_NOT_FOUND",
            message: "The payer account was not found"
          } :
          responseStatus === "pending" ? {
            code: "AWAITING_PAYER_CONFIRMATION",
            message: "Waiting for the payer to confirm the payment"
          } : {
            code: "TRANSACTION_TIMEOUT",
            message: "The transaction timed out"
          },
        requestTimestamp: new Date(Date.now() - 60000).toISOString(), // 1 minute ago
        responseTimestamp: responseStatus === "pending" ? null : new Date().toISOString(),
      }

      setApiResponse(mockResponse)
      setIsLoading(false)
      setTestScenario(null)
    }, 1500)
  }

  // Get status badge
  const getStatusBadge = (status: TransactionStatus) => {
    switch (status) {
      case "SUCCESSFUL":
        return (
          <Badge className="bg-success text-white">
            <Check className="h-3 w-3 mr-1" /> Success
          </Badge>
        )
      case "FAILED":
        return (
          <Badge className="bg-destructive text-white">
            <AlertTriangle className="h-3 w-3 mr-1" /> Failed
          </Badge>
        )
      case "PENDING":
        return (
          <Badge className="bg-yellow-500 text-white">
            <Clock className="h-3 w-3 mr-1" /> Pending
          </Badge>
        )
      case "TIMEOUT":
        return (
          <Badge className="bg-gray-500 text-white">
            <Server className="h-3 w-3 mr-1" /> Timeout
          </Badge>
        )
      default:
        return <Badge>{status}</Badge>
    }
  }

  return (
    <div>
      <div className="mb-6 md:mb-8">
        <h1 className="text-2xl md:text-3xl font-bold">API Testing</h1>
        <p className="text-sm md:text-base text-gray-500">Test MTN MoMo API functionality in the sandbox environment</p>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>MTN MoMo API Testing</CardTitle>
              <CardDescription>
                Test payment requests and status checks in the sandbox environment
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="mb-4">
                  <TabsTrigger value="request-to-pay">Request to Pay</TabsTrigger>
                  <TabsTrigger value="status-check">Check Status</TabsTrigger>
                </TabsList>

                <TabsContent value="request-to-pay">
                  <Form {...requestToPayForm}>
                    <form onSubmit={requestToPayForm.handleSubmit(onRequestToPaySubmit)} className="space-y-4">
                      <FormField
                        control={requestToPayForm.control}
                        name="phoneNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Phone Number *</FormLabel>
                            <FormControl>
                              <Input placeholder="+************" {...field} />
                            </FormControl>
                            <FormDescription>
                              The phone number to request payment from
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={requestToPayForm.control}
                        name="amount"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Amount (XAF) *</FormLabel>
                            <FormControl>
                              <Input type="number" min="100" placeholder="1000" {...field} />
                            </FormControl>
                            <FormDescription>
                              Amount in XAF to request (minimum 100 XAF)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={requestToPayForm.control}
                        name="externalId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>External ID *</FormLabel>
                            <div className="flex gap-2">
                              <FormControl>
                                <Input {...field} />
                              </FormControl>
                              <Button 
                                type="button" 
                                variant="outline" 
                                size="icon"
                                onClick={generateExternalId}
                                title="Generate new UUID"
                              >
                                <RefreshCw className="h-4 w-4" />
                              </Button>
                            </div>
                            <FormDescription>
                              Your reference ID for this transaction
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      
                      <FormField
                        control={requestToPayForm.control}
                        name="description"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Description *</FormLabel>
                            <FormControl>
                              <Textarea placeholder="Payment description" {...field} />
                            </FormControl>
                            <FormDescription>
                              Description of what this payment is for
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="flex flex-col gap-4 pt-4">
                        <div className="grid grid-cols-2 sm:flex sm:flex-wrap gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => setTestScenario("success")}
                            className="text-xs md:text-sm border-success text-success hover:bg-success/10"
                          >
                            <Check className="h-3 w-3 md:h-4 md:w-4 mr-1" /> Success
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => setTestScenario("failure")}
                            className="text-xs md:text-sm border-destructive text-destructive hover:bg-destructive/10"
                          >
                            <AlertTriangle className="h-3 w-3 md:h-4 md:w-4 mr-1" /> Failure
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => setTestScenario("pending")}
                            className="text-xs md:text-sm border-yellow-500 text-yellow-500 hover:bg-yellow-500/10"
                          >
                            <Clock className="h-3 w-3 md:h-4 md:w-4 mr-1" /> Pending
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => setTestScenario("timeout")}
                            className="text-xs md:text-sm border-gray-500 text-gray-500 hover:bg-gray-500/10"
                          >
                            <Server className="h-3 w-3 md:h-4 md:w-4 mr-1" /> Timeout
                          </Button>
                        </div>
                        
                        <Button 
                          type="submit" 
                          className="bg-blue-600 hover:bg-blue-700 text-xs md:text-sm"
                          disabled={isLoading}
                        >
                          {isLoading ? (
                            <>
                              <RefreshCw className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2 animate-spin" />
                              Processing...
                            </>
                          ) : (
                            <>
                              <Zap className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                              Initiate Test Payment
                            </>
                          )}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </TabsContent>

                <TabsContent value="status-check">
                  <Form {...statusCheckForm}>
                    <form onSubmit={statusCheckForm.handleSubmit(onStatusCheckSubmit)} className="space-y-4">
                      <FormField
                        control={statusCheckForm.control}
                        name="transactionId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Transaction ID *</FormLabel>
                            <FormControl>
                              <Input placeholder="txn_1234567890" {...field} />
                            </FormControl>
                            <FormDescription>
                              The transaction ID returned from the request-to-pay call
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="flex flex-col gap-4 pt-4">
                        <div className="grid grid-cols-2 sm:flex sm:flex-wrap gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => setTestScenario("success")}
                            className="text-xs md:text-sm border-success text-success hover:bg-success/10"
                          >
                            <Check className="h-3 w-3 md:h-4 md:w-4 mr-1" /> Success
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => setTestScenario("failure")}
                            className="text-xs md:text-sm border-destructive text-destructive hover:bg-destructive/10"
                          >
                            <AlertTriangle className="h-3 w-3 md:h-4 md:w-4 mr-1" /> Failure
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => setTestScenario("pending")}
                            className="text-xs md:text-sm border-yellow-500 text-yellow-500 hover:bg-yellow-500/10"
                          >
                            <Clock className="h-3 w-3 md:h-4 md:w-4 mr-1" /> Pending
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => setTestScenario("timeout")}
                            className="text-xs md:text-sm border-gray-500 text-gray-500 hover:bg-gray-500/10"
                          >
                            <Server className="h-3 w-3 md:h-4 md:w-4 mr-1" /> Timeout
                          </Button>
                        </div>
                        
                        <Button 
                          type="submit" 
                          className="bg-blue-600 hover:bg-blue-700 text-xs md:text-sm"
                          disabled={isLoading}
                        >
                          {isLoading ? (
                            <>
                              <RefreshCw className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2 animate-spin" />
                              Checking...
                            </>
                          ) : (
                            <>
                              <ArrowRight className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                              Check Payment Status
                            </>
                          )}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </TabsContent>
              </Tabs>

              {/* API Response */}
              {apiResponse && (
                <div className="mt-8">
                  <Separator className="my-4" />
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold">API Response</h3>
                      <div className="flex items-center gap-2">
                        {getStatusBadge(apiResponse.status)}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyToClipboard(JSON.stringify(apiResponse, null, 2))}
                        >
                          {copied ? (
                            <>
                              <Check className="h-4 w-4 mr-1" /> Copied
                            </>
                          ) : (
                            <>
                              <Copy className="h-4 w-4 mr-1" /> Copy JSON
                            </>
                          )}
                        </Button>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="text-xs md:text-sm font-semibold mb-2">Transaction Details</h4>
                        <div className="space-y-2">
                          <div className="flex flex-col md:flex-row md:justify-between">
                            <span className="text-xs md:text-sm text-gray-500">Transaction ID:</span>
                            <span className="text-xs md:text-sm font-mono break-all">{apiResponse.transactionId}</span>
                          </div>
                          <div className="flex flex-col md:flex-row md:justify-between">
                            <span className="text-xs md:text-sm text-gray-500">External ID:</span>
                            <span className="text-xs md:text-sm font-mono break-all">{apiResponse.externalId}</span>
                          </div>
                          <div className="flex flex-col md:flex-row md:justify-between">
                            <span className="text-xs md:text-sm text-gray-500">Amount:</span>
                            <span className="text-xs md:text-sm font-mono">{apiResponse.amount} {apiResponse.currency}</span>
                          </div>
                          <div className="flex flex-col md:flex-row md:justify-between">
                            <span className="text-xs md:text-sm text-gray-500">Payer:</span>
                            <span className="text-xs md:text-sm font-mono">{apiResponse.payerParty.partyId}</span>
                          </div>
                          <div className="flex flex-col md:flex-row md:justify-between">
                            <span className="text-xs md:text-sm text-gray-500">Status:</span>
                            <span className="text-xs md:text-sm">{getStatusBadge(apiResponse.status)}</span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="text-xs md:text-sm font-semibold mb-2">Response Details</h4>
                        <div className="space-y-2">
                          <div className="flex flex-col md:flex-row md:justify-between">
                            <span className="text-xs md:text-sm text-gray-500">Status Code:</span>
                            <span className="text-xs md:text-sm font-mono">{apiResponse.reason?.code || "N/A"}</span>
                          </div>
                          <div className="flex flex-col md:flex-row md:justify-between">
                            <span className="text-xs md:text-sm text-gray-500">Message:</span>
                            <span className="text-xs md:text-sm">{apiResponse.reason?.message || "N/A"}</span>
                          </div>
                          <div className="flex flex-col md:flex-row md:justify-between">
                            <span className="text-xs md:text-sm text-gray-500">Request Time:</span>
                            <span className="text-xs md:text-sm">
                              {(() => {
                                const date = new Date(apiResponse.requestTimestamp);
                                return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
                              })()}
                            </span>
                          </div>
                          <div className="flex flex-col md:flex-row md:justify-between">
                            <span className="text-xs md:text-sm text-gray-500">Response Time:</span>
                            <span className="text-xs md:text-sm">
                              {apiResponse.responseTimestamp 
                                ? (() => {
                                    const date = new Date(apiResponse.responseTimestamp);
                                    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`;
                                  })()
                                : "Pending"}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto text-sm mt-4">
                      {JSON.stringify(apiResponse, null, 2)}
                    </pre>

                    {apiResponse.transactionId && activeTab === "request-to-pay" && (
                      <div className="flex justify-end">
                        <Button variant="outline" onClick={copyTransactionIdToStatusCheck}>
                          <ArrowRight className="h-4 w-4 mr-2" />
                          Check Status of This Transaction
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <div>
          {/* API Information */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>API Endpoints</CardTitle>
              <CardDescription>Sandbox MTN MoMo API endpoints</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-sm font-semibold mb-1">Base URL</h3>
                <code className="block p-2 bg-gray-100 rounded-md text-xs font-mono">
                  https://sandbox-api.starterpay.cm/v1
                </code>
              </div>

              <div>
                <h3 className="text-sm font-semibold mb-1">Request to Pay</h3>
                <code className="block p-2 bg-gray-100 rounded-md text-xs font-mono">
                  POST /collections/request-to-pay
                </code>
              </div>

              <div>
                <h3 className="text-sm font-semibold mb-1">Check Status</h3>
                <code className="block p-2 bg-gray-100 rounded-md text-xs font-mono">
                  GET /collections/{"{transactionId}"}/status
                </code>
              </div>
            </CardContent>
          </Card>

          {/* Test Scenarios */}
          <Card>
            <CardHeader>
              <CardTitle>Test Scenarios</CardTitle>
              <CardDescription>Simulate different API responses</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert className="bg-green-50 border-green-200">
                <Check className="h-4 w-4 text-green-500" />
                <AlertTitle className="text-green-800">Success</AlertTitle>
                <AlertDescription className="text-green-700 text-xs">
                  Simulates a successful payment transaction where the payer confirms and completes the payment.
                </AlertDescription>
              </Alert>

              <Alert className="bg-red-50 border-red-200">
                <AlertTriangle className="h-4 w-4 text-red-500" />
                <AlertTitle className="text-red-800">Failure</AlertTitle>
                <AlertDescription className="text-red-700 text-xs">
                  Simulates a failed payment due to various reasons like insufficient funds, invalid account, etc.
                </AlertDescription>
              </Alert>

              <Alert className="bg-yellow-50 border-yellow-200">
                <Clock className="h-4 w-4 text-yellow-500" />
                <AlertTitle className="text-yellow-800">Pending</AlertTitle>
                <AlertDescription className="text-yellow-700 text-xs">
                  Simulates a pending payment that is awaiting confirmation from the payer.
                </AlertDescription>
              </Alert>

              <Alert className="bg-gray-50 border-gray-200">
                <Server className="h-4 w-4 text-gray-500" />
                <AlertTitle className="text-gray-800">Timeout</AlertTitle>
                <AlertDescription className="text-gray-700 text-xs">
                  Simulates a transaction that timed out due to network issues or server problems.
                </AlertDescription>
              </Alert>
            </CardContent>
            <CardFooter>
              <p className="text-xs text-gray-500">
                Click any of the test scenario buttons before submitting the form to simulate different API responses.
              </p>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  )
}