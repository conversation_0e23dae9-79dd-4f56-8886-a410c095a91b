/**
 * Simple test script to debug MTN MoMo API connectivity
 */

const https = require('https');

// Disable SSL verification for sandbox testing
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

const subscriptionKey = '4150dc3e4b3b49f2b24b3e1fe9d3e2b7';
const baseUrl = 'https://sandbox.momodeveloper.mtn.com';

console.log('🔍 Testing MTN MoMo API connectivity...');
console.log('Base URL:', baseUrl);
console.log('Subscription Key (first 10 chars):', subscriptionKey.substring(0, 10) + '...');

// Test 1: Simple connectivity test
async function testConnectivity() {
  console.log('\n📡 Test 1: Basic connectivity test');
  
  try {
    const response = await fetch(baseUrl, {
      method: 'GET',
      signal: AbortSignal.timeout(15000) // 15 second timeout
    });
    
    console.log('✅ Base URL is reachable');
    console.log('Status:', response.status);
    console.log('Headers:', Object.fromEntries(response.headers.entries()));
    
  } catch (error) {
    console.log('❌ Base URL connectivity failed:', error.message);
    console.log('Error code:', error.code);
  }
}

// Test 2: Test API user creation endpoint
async function testApiUserEndpoint() {
  console.log('\n👤 Test 2: API User endpoint test');

  const userId = 'test-user-' + Date.now();

  // Try different callback host formats
  const callbackHosts = [
    'webhook.site',
    'https://webhook.site',
    'https://webhook.site/unique-id',
    'example.com',
    'https://example.com'
  ];

  for (const callbackHost of callbackHosts) {
    console.log(`\n  Testing with callback host: ${callbackHost}`);

    try {
      const response = await fetch(`${baseUrl}/v1_0/apiuser`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Reference-Id': userId + '-' + callbackHosts.indexOf(callbackHost),
          'Ocp-Apim-Subscription-Key': subscriptionKey,
        },
        body: JSON.stringify({
          providerCallbackHost: callbackHost
        }),
        signal: AbortSignal.timeout(15000)
      });

      console.log('  Status:', response.status);

      if (response.ok) {
        console.log('  ✅ Success! API User created');
        const responseText = await response.text();
        console.log('  Response:', responseText);
        return; // Exit on first success
      } else {
        const errorText = await response.text();
        console.log('  ❌ Error response:', errorText);
      }

    } catch (error) {
      console.log('  ❌ Request failed:', error.message);
    }
  }
}

// Test 3: Test token endpoint (this is where our error occurs)
async function testTokenEndpoint() {
  console.log('\n🔑 Test 3: Token endpoint test (where the error occurs)');
  
  // Use dummy credentials for testing
  const dummyUserId = 'test-user-id';
  const dummyApiKey = 'test-api-key';
  const credentials = Buffer.from(`${dummyUserId}:${dummyApiKey}`).toString('base64');
  
  try {
    const response = await fetch(`${baseUrl}/collection/token`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${credentials}`,
        'Ocp-Apim-Subscription-Key': subscriptionKey,
        'X-Target-Environment': 'sandbox',
      },
      signal: AbortSignal.timeout(15000)
    });
    
    console.log('✅ Token endpoint responded');
    console.log('Status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('Error response:', errorText);
    } else {
      const responseData = await response.json();
      console.log('Success response:', responseData);
    }
    
  } catch (error) {
    console.log('❌ Token endpoint failed:', error.message);
    console.log('Error code:', error.code);
    console.log('Error name:', error.name);
    if (error.cause) {
      console.log('Cause:', error.cause);
    }
  }
}

// Run all tests
async function runTests() {
  await testConnectivity();
  await testApiUserEndpoint();
  await testTokenEndpoint();
  
  console.log('\n🏁 Test completed');
}

runTests().catch(console.error);
