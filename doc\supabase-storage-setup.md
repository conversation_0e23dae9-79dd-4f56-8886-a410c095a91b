# Supabase Storage Setup for KYC Documents

## 🗂️ Storage Bucket Configuration

### Create KYC Documents Bucket

1. **Go to Supabase Dashboard** → Storage
2. **Create new bucket**: `kyc-documents`
3. **Set bucket as private** (not public)
4. **Configure RLS policies**

### Storage Policies

```sql
-- Policy: Users can upload their own KYC documents
CREATE POLICY "Users can upload own KYC documents" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'kyc-documents' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy: Users can view their own KYC documents
CREATE POLICY "Users can view own KYC documents" ON storage.objects
FOR SELECT USING (
  bucket_id = 'kyc-documents' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy: Admins can view all KYC documents
CREATE POLICY "Ad<PERSON> can view all KYC documents" ON storage.objects
FOR SELECT USING (
  bucket_id = 'kyc-documents'
  AND EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = auth.uid() AND role = 'ADMIN'
  )
);

-- Policy: Users can update their own KYC documents
CREATE POLICY "Users can update own KYC documents" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'kyc-documents' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy: Users can delete their own KYC documents
CREATE POLICY "Users can delete own KYC documents" ON storage.objects
FOR DELETE USING (
  bucket_id = 'kyc-documents' 
  AND auth.uid()::text = (storage.foldername(name))[1]
);
```

## 📁 File Organization Structure

```
kyc-documents/
├── {user_id}/
│   ├── student_id_card.{ext}
│   ├── registration_receipt.{ext}
│   ├── passport_photo.{ext}
│   └── national_id.{ext} (optional)
```

## 🔧 File Upload Configuration

### Allowed File Types
- **Images**: JPG, JPEG, PNG
- **Documents**: PDF
- **Max Size**: 2MB per file

### File Naming Convention
- `{user_id}/{document_type}.{extension}`
- Example: `123e4567-e89b-12d3-a456-************/student_id_card.jpg`

## 🛠️ Implementation Notes

### Environment Variables
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

### File Upload Process
1. **Validate file** (type, size)
2. **Generate file path**: `{userId}/{documentType}.{extension}`
3. **Upload to Supabase Storage**
4. **Save file metadata** to `kyc_documents` table
5. **Update KYC application** status

### File Access Process
1. **Students**: Can only access their own files
2. **Admins**: Can access all files for review
3. **Generate signed URLs** for secure access
4. **Set appropriate expiration** times

## 🔐 Security Considerations

- All files are **private by default**
- **RLS policies** control access
- **Signed URLs** for temporary access
- **File type validation** on upload
- **Size limits** enforced
- **User isolation** via folder structure
