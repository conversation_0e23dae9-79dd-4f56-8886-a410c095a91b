// Datasource points to Supabase Postgres
// The POSTGRES_URL env var is provided by Vercel (and locally via .env.local)
datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_URL")
  // Only manage our own public schema; Supabase manages "auth"
  schemas  = ["public"]
  relationMode = "prisma"
}

generator client {
  provider = "prisma-client-js"
  previewFeatures = ["multiSchema"]
}

model User {
  id       String   @id @default(uuid()) @db.Uuid
  role     UserType
  studentProfile  StudentProfile?
  adminProfile    AdminProfile?

  // Sandbox relations
  sandboxWallet       SandboxWallet?
  sandboxApiKeys      SandboxApiKey[]
  sandboxTransactions SandboxTransaction[] @relation("UserTransactions")

  // KYC relations
  kycApplicationsReviewed KycApplication[] @relation("KycReviewer")

  @@map("users")
  @@schema("public")
}

model StudentProfile {
  id       String   @id @default(uuid()) @db.Uuid
  fullName String   @map("full_name")
  email    String   @unique
  phoneNumber String? @map("phone_number")
  createdAt DateTime @default(now()) @map("created_at")
  kycStatus   KycStatus @default(NOT_STARTED) @map("kyc_status")
  kycCompletedAt DateTime? @map("kyc_completed_at")
  kycReviewedAt DateTime? @map("kyc_reviewed_at")

  // relation back to central user
  userId  String   @unique @db.Uuid
  user    User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // KYC application relation
  kycApplication KycApplication?

  @@map("student_profiles")
  @@schema("public")
}

model AdminProfile {
  id           String   @id @default(uuid()) @db.Uuid
  fullName     String   @map("full_name")
  email        String   @unique
  phoneNumber  String?
  location     String
  profession   String
  organization String?
  experience   String
  motivation   String
  availability String
  createdAt    DateTime @default(now()) @map("created_at")

  // relation back to central user
  userId String @unique @db.Uuid
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("admin_profiles")
  @@schema("public")
}

enum UserType {
  @@schema("public")
  STUDENT
  ADMIN
}

enum KycStatus {
  @@schema("public")
  NOT_STARTED
  IN_PROGRESS
  PENDING_REVIEW
  APPROVED
  REJECTED
}

// Sandbox-specific models for MTN MoMo Collections API
model SandboxWallet {
  id        String   @id @default(uuid()) @db.Uuid
  userId    String   @unique @db.Uuid
  balance   Decimal  @default(100000) @db.Decimal(15, 2) // Default 100,000 XAF
  currency  String   @default("XAF")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  user         User                      @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions SandboxTransaction[]     @relation("WalletTransactions")

  @@map("sandbox_wallets")
  @@schema("public")
}

model SandboxApiKey {
  id          String   @id @default(uuid()) @db.Uuid
  userId      String   @db.Uuid
  keyName     String   @map("key_name")
  keyHash     String   @map("key_hash") // Hashed version of the key
  environment String   @default("sandbox")
  isActive    Boolean  @default(true) @map("is_active")
  usageCount  Int      @default(0) @map("usage_count")
  rateLimit   Int      @default(100) @map("rate_limit") // Requests per hour
  createdAt   DateTime @default(now()) @map("created_at")
  lastUsedAt  DateTime? @map("last_used_at")

  // Relations
  user         User                      @relation(fields: [userId], references: [id], onDelete: Cascade)
  transactions SandboxTransaction[]

  @@map("sandbox_api_keys")
  @@schema("public")
}

model SandboxTransaction {
  id                    String                    @id @default(uuid()) @db.Uuid
  userId                String                    @db.Uuid
  apiKeyId              String?                   @db.Uuid @map("api_key_id")
  externalId            String                    @map("external_id") // Student's transaction ID
  amount                Decimal                   @db.Decimal(15, 2)
  currency              String                    @default("XAF")
  type                  SandboxTransactionType
  status                SandboxTransactionStatus  @default(PENDING)

  // MTN MoMo specific fields
  mtnTransactionId      String?                   @map("mtn_transaction_id") // MTN's transaction ID
  mtnReferenceId        String                    @unique @map("mtn_reference_id") // Our reference ID sent to MTN

  // Payer information
  payerPhone            String                    @map("payer_phone")
  payerMessage          String?                   @map("payer_message")
  payeeNote             String?                   @map("payee_note")

  // Metadata
  callbackUrl           String?                   @map("callback_url")
  failureReason         String?                   @map("failure_reason")

  createdAt             DateTime                  @default(now()) @map("created_at")
  updatedAt             DateTime                  @updatedAt @map("updated_at")
  completedAt           DateTime?                 @map("completed_at")

  // Relations
  user      User             @relation("UserTransactions", fields: [userId], references: [id], onDelete: Cascade, map: "sandbox_transactions_user_fkey")
  apiKey    SandboxApiKey?   @relation(fields: [apiKeyId], references: [id])
  wallet    SandboxWallet    @relation("WalletTransactions", fields: [userId], references: [userId], map: "sandbox_transactions_wallet_fkey")

  @@map("sandbox_transactions")
  @@schema("public")
}

enum SandboxTransactionType {
  @@schema("public")
  COLLECTION  // Request to Pay
}

enum SandboxTransactionStatus {
  @@schema("public")
  PENDING     // Waiting for MTN response
  SUCCESSFUL  // Payment completed
  FAILED      // Payment failed
  CANCELLED   // Payment cancelled
}

// KYC Application model - matches onboarding form exactly
model KycApplication {
  id        String   @id @default(uuid()) @db.Uuid
  userId    String   @unique @db.Uuid @map("user_id")

  // Step 1: University Information
  universityName    String   @map("university_name")
  faculty          String
  programOfStudy   String   @map("program_of_study")
  graduationYear   String   @map("graduation_year")
  schoolAddress    String?  @map("school_address")
  schoolContact    String?  @map("school_contact")
  schoolWebsite    String?  @map("school_website")

  // Step 3: Project Purpose
  apiPurpose         String  @map("api_purpose") // academic, hackathon, startup, learning
  projectName        String? @map("project_name")
  projectDescription String  @map("project_description")
  githubLink         String? @map("github_link")

  // Step 4: Student Profile
  studentIdNumber    String  @map("student_id_number")
  yearOfStudy        String  @map("year_of_study") // 100, 200, 300, 400, 500, final
  preferredLanguage  String? @map("preferred_language") // english, french
  termsAccepted      Boolean @map("terms_accepted")

  // Admin Review Fields
  reviewedBy    String?   @db.Uuid @map("reviewed_by")
  reviewNotes   String?   @map("review_notes")
  reviewedAt    DateTime? @map("reviewed_at")

  // Timestamps
  submittedAt DateTime  @default(now()) @map("submitted_at")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // Relations
  user           User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  studentProfile StudentProfile    @relation(fields: [userId], references: [userId])
  reviewer       User?             @relation("KycReviewer", fields: [reviewedBy], references: [id])
  documents      KycDocument[]

  @@map("kyc_applications")
  @@schema("public")
}

// KYC Documents - stored in Supabase Storage
model KycDocument {
  id            String   @id @default(uuid()) @db.Uuid
  applicationId String   @db.Uuid @map("application_id")
  documentType  String   @map("document_type") // studentIdCard, registrationReceipt, passportPhoto, nationalId
  fileName      String   @map("file_name")
  filePath      String   @map("file_path") // Supabase Storage path
  fileSize      Int      @map("file_size")
  mimeType      String   @map("mime_type")
  uploadedAt    DateTime @default(now()) @map("uploaded_at")

  // Relations
  application KycApplication @relation(fields: [applicationId], references: [id], onDelete: Cascade)

  @@map("kyc_documents")
  @@schema("public")
}
