"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Search, ChevronDown, ChevronUp, Eye } from "lucide-react"

export default function TransactionsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [typeFilter, setTypeFilter] = useState("all")
  const [expandedTransaction, setExpandedTransaction] = useState<string | null>(null)

  // Mock transaction data
  const transactions = [
    {
      id: "test_txn_001",
      externalId: "ext_001",
      type: "Collection",
      amount: 10000,
      currency: "XAF",
      status: "success",
      createdAt: "2025-06-15T14:30:00Z",
      completedAt: "2025-06-15T14:30:45Z",
      phone: "+237612345678",
      description: "Test payment collection",
      payerMessage: "Thank you for your payment",
      payeeNote: "Product purchase",
      payload: {
        requestTimestamp: "2025-06-15T14:30:00Z",
        responseTimestamp: "2025-06-15T14:30:45Z",
        transactionId: "test_txn_001",
        status: "SUCCESSFUL",
        reason: {
          code: "PAYER_COMPLETED",
          message: "The payer has completed the payment"
        },
        externalId: "ext_001",
        amount: "10000",
        currency: "XAF",
        payerParty: {
          partyIdType: "MSISDN",
          partyId: "+237612345678"
        },
        payeeParty: {
          partyIdType: "MSISDN",
          partyId: "+************"
        }
      }
    },
    {
      id: "test_txn_002",
      externalId: "ext_002",
      type: "Disbursement",
      amount: 5000,
      currency: "XAF",
      status: "success",
      createdAt: "2025-06-15T13:45:00Z",
      completedAt: "2025-06-15T13:46:20Z",
      phone: "+237698765432",
      description: "Test payout",
      payerMessage: "Payout for services",
      payeeNote: "Contractor payment",
      payload: {
        requestTimestamp: "2025-06-15T13:45:00Z",
        responseTimestamp: "2025-06-15T13:46:20Z",
        transactionId: "test_txn_002",
        status: "SUCCESSFUL",
        reason: {
          code: "PAYEE_RECEIVED",
          message: "The payee has received the funds"
        },
        externalId: "ext_002",
        amount: "5000",
        currency: "XAF",
        payerParty: {
          partyIdType: "MSISDN",
          partyId: "+************"
        },
        payeeParty: {
          partyIdType: "MSISDN",
          partyId: "+237698765432"
        }
      }
    },
    {
      id: "test_txn_003",
      externalId: "ext_003",
      type: "Collection",
      amount: 15000,
      currency: "XAF",
      status: "failed",
      createdAt: "2025-06-15T12:30:00Z",
      completedAt: "2025-06-15T12:31:15Z",
      phone: "+************",
      description: "Failed test transaction",
      payerMessage: "Payment for services",
      payeeNote: "Monthly subscription",
      payload: {
        requestTimestamp: "2025-06-15T12:30:00Z",
        responseTimestamp: "2025-06-15T12:31:15Z",
        transactionId: "test_txn_003",
        status: "FAILED",
        reason: {
          code: "PAYER_NOT_FOUND",
          message: "The payer account was not found"
        },
        externalId: "ext_003",
        amount: "15000",
        currency: "XAF",
        payerParty: {
          partyIdType: "MSISDN",
          partyId: "+************"
        },
        payeeParty: {
          partyIdType: "MSISDN",
          partyId: "+************"
        }
      }
    },
    {
      id: "test_txn_004",
      externalId: "ext_004",
      type: "Collection",
      amount: 7500,
      currency: "XAF",
      status: "pending",
      createdAt: "2025-06-15T11:15:00Z",
      completedAt: null,
      phone: "+************",
      description: "Pending test transaction",
      payerMessage: "Payment for products",
      payeeNote: "Product purchase",
      payload: {
        requestTimestamp: "2025-06-15T11:15:00Z",
        responseTimestamp: null,
        transactionId: "test_txn_004",
        status: "PENDING",
        reason: {
          code: "AWAITING_PAYER_CONFIRMATION",
          message: "Waiting for the payer to confirm the payment"
        },
        externalId: "ext_004",
        amount: "7500",
        currency: "XAF",
        payerParty: {
          partyIdType: "MSISDN",
          partyId: "+************"
        },
        payeeParty: {
          partyIdType: "MSISDN",
          partyId: "+************"
        }
      }
    },
    {
      id: "test_txn_005",
      externalId: "ext_005",
      type: "Disbursement",
      amount: 12000,
      currency: "XAF",
      status: "timeout",
      createdAt: "2025-06-15T10:00:00Z",
      completedAt: "2025-06-15T10:03:00Z",
      phone: "+237645678901",
      description: "Timeout test transaction",
      payerMessage: "Payout for services",
      payeeNote: "Service payment",
      payload: {
        requestTimestamp: "2025-06-15T10:00:00Z",
        responseTimestamp: "2025-06-15T10:03:00Z",
        transactionId: "test_txn_005",
        status: "FAILED",
        reason: {
          code: "TRANSACTION_TIMEOUT",
          message: "The transaction timed out"
        },
        externalId: "ext_005",
        amount: "12000",
        currency: "XAF",
        payerParty: {
          partyIdType: "MSISDN",
          partyId: "+************"
        },
        payeeParty: {
          partyIdType: "MSISDN",
          partyId: "+237645678901"
        }
      }
    }
  ]

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return "N/A"
    const date = new Date(dateString)
    // Use a fixed format rather than locale-dependent formatting to avoid hydration errors
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`
  }

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "success":
        return <Badge className="bg-success text-white">Success</Badge>
      case "failed":
        return <Badge className="bg-destructive text-white">Failed</Badge>
      case "pending":
        return <Badge className="bg-yellow-500 text-white">Pending</Badge>
      case "timeout":
        return <Badge className="bg-gray-500 text-white">Timeout</Badge>
      default:
        return <Badge className="bg-gray-500 text-white">{status}</Badge>
    }
  }

  // Filter transactions
  const filteredTransactions = transactions.filter((transaction) => {
    // Filter by search term
    const searchMatch =
      searchTerm === "" ||
      transaction.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.externalId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.phone.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.description.toLowerCase().includes(searchTerm.toLowerCase())

    // Filter by status
    const statusMatch = statusFilter === "all" || transaction.status === statusFilter

    // Filter by type
    const typeMatch = typeFilter === "all" || transaction.type === typeFilter

    return searchMatch && statusMatch && typeMatch
  })

  // Toggle transaction details
  const toggleTransactionDetails = (transactionId: string) => {
    if (expandedTransaction === transactionId) {
      setExpandedTransaction(null)
    } else {
      setExpandedTransaction(transactionId)
    }
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Transactions</h1>
        <p className="text-gray-500">View and manage your test transactions</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Test Transactions</CardTitle>
          <CardDescription>All your sandbox transactions</CardDescription>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  type="text"
                  placeholder="Search by ID, external ID, phone..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <div className="flex gap-4">
              <div className="w-40">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="success">Success</SelectItem>
                    <SelectItem value="failed">Failed</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="timeout">Timeout</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="w-40">
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="Collection">Collection</SelectItem>
                    <SelectItem value="Disbursement">Disbursement</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Transactions Table */}
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Transaction ID</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Phone</TableHead>
                  <TableHead>Created At</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTransactions.map((transaction) => (
                  <React.Fragment key={transaction.id}>
                    <TableRow>
                      <TableCell className="font-mono text-sm">{transaction.id}</TableCell>
                      <TableCell>{transaction.type}</TableCell>
                      <TableCell>{transaction.amount.toLocaleString()} {transaction.currency}</TableCell>
                      <TableCell>{getStatusBadge(transaction.status)}</TableCell>
                      <TableCell>{transaction.phone}</TableCell>
                      <TableCell>{formatDate(transaction.createdAt)}</TableCell>
                      <TableCell>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => toggleTransactionDetails(transaction.id)}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          {expandedTransaction === transaction.id ? (
                            <ChevronUp className="h-4 w-4" />
                          ) : (
                            <ChevronDown className="h-4 w-4" />
                          )}
                        </Button>
                      </TableCell>
                    </TableRow>
                    {expandedTransaction === transaction.id && (
                      <TableRow>
                        <TableCell colSpan={7} className="bg-gray-50 p-0">
                          <div className="p-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                              <div>
                                <h4 className="text-sm font-semibold mb-2">Transaction Details</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm">
                                  <div className="text-gray-500">External ID:</div>
                                  <div>{transaction.externalId}</div>
                                  <div className="text-gray-500">Description:</div>
                                  <div>{transaction.description}</div>
                                  <div className="text-gray-500">Payer Message:</div>
                                  <div>{transaction.payerMessage}</div>
                                  <div className="text-gray-500">Payee Note:</div>
                                  <div>{transaction.payeeNote}</div>
                                </div>
                              </div>
                              <div>
                                <h4 className="text-sm font-semibold mb-2">Timeline</h4>
                                <div className="grid grid-cols-2 gap-2 text-sm">
                                  <div className="text-gray-500">Created At:</div>
                                  <div>{formatDate(transaction.createdAt)}</div>
                                  <div className="text-gray-500">Completed At:</div>
                                  <div>{transaction.completedAt ? formatDate(transaction.completedAt) : "N/A"}</div>
                                  <div className="text-gray-500">Status:</div>
                                  <div>{getStatusBadge(transaction.status)}</div>
                                </div>
                              </div>
                            </div>
                            <Accordion type="single" collapsible className="w-full">
                              <AccordionItem value="payload">
                                <AccordionTrigger className="text-sm font-semibold">
                                  View Full API Payload
                                </AccordionTrigger>
                                <AccordionContent>
                                  <pre className="bg-gray-100 p-4 rounded-md overflow-x-auto text-sm">
                                    {JSON.stringify(transaction.payload, null, 2)}
                                  </pre>
                                </AccordionContent>
                              </AccordionItem>
                            </Accordion>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                ))}
                {filteredTransactions.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      No transactions found matching your filters.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}