-- CreateTable
CREATE TABLE "public"."kyc_applications" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "user_id" UUID NOT NULL,
    "university_name" TEXT NOT NULL,
    "faculty" TEXT NOT NULL,
    "program_of_study" TEXT NOT NULL,
    "graduation_year" TEXT NOT NULL,
    "school_address" TEXT,
    "school_contact" TEXT,
    "school_website" TEXT,
    "api_purpose" TEXT NOT NULL,
    "project_name" TEXT,
    "project_description" TEXT NOT NULL,
    "github_link" TEXT,
    "student_id_number" TEXT NOT NULL,
    "year_of_study" TEXT NOT NULL,
    "preferred_language" TEXT,
    "terms_accepted" BOOLEAN NOT NULL,
    "reviewed_by" UUID,
    "review_notes" TEXT,
    "reviewed_at" TIMESTAMP(3),
    "submitted_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "kyc_applications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "public"."kyc_documents" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "application_id" UUID NOT NULL,
    "document_type" TEXT NOT NULL,
    "file_name" TEXT NOT NULL,
    "file_path" TEXT NOT NULL,
    "file_size" INTEGER NOT NULL,
    "mime_type" TEXT NOT NULL,
    "uploaded_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "kyc_documents_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "kyc_applications_user_id_key" ON "public"."kyc_applications"("user_id");

-- AddForeignKey
ALTER TABLE "public"."kyc_applications" ADD CONSTRAINT "kyc_applications_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."kyc_applications" ADD CONSTRAINT "kyc_applications_reviewed_by_fkey" FOREIGN KEY ("reviewed_by") REFERENCES "public"."users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."kyc_documents" ADD CONSTRAINT "kyc_documents_application_id_fkey" FOREIGN KEY ("application_id") REFERENCES "public"."kyc_applications"("id") ON DELETE CASCADE ON UPDATE CASCADE;
