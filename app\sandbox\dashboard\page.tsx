"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"


interface WalletData {
  id: string
  balance: number
  currency: string
  createdAt: string
  recentTransactions: Transaction[]
}

interface ApiKeyData {
  id: string
  keyName: string
  environment: string
  isActive: boolean
  usageCount: number
  rateLimit: number
  createdAt: string
  lastUsedAt: string | null
}

interface Transaction {
  id: string
  externalId: string
  amount: number
  currency: string
  status: string
  type: string
  createdAt: string
  completedAt: string | null
}

export default function SandboxDashboardPage() {
  // State for real data
  const [walletData, setWalletData] = useState<WalletData | null>(null)
  const [apiKeys, setApiKeys] = useState<ApiKeyData[]>([])
  const [kycStatus, setKycStatus] = useState<"pending" | "approved" | "rejected" | "not_started">("not_started")
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch wallet and API key data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true)
        setError(null)

        // Fetch wallet data
        const walletResponse = await fetch('/api/sandbox/wallet')
        if (!walletResponse.ok) {
          throw new Error('Failed to fetch wallet data')
        }
        const walletResult = await walletResponse.json()
        if (walletResult.success) {
          setWalletData(walletResult.data)
        }

        // Fetch API keys
        const apiKeysResponse = await fetch('/api/sandbox/api-keys')
        if (!apiKeysResponse.ok) {
          throw new Error('Failed to fetch API keys')
        }
        const apiKeysResult = await apiKeysResponse.json()
        if (apiKeysResult.success) {
          setApiKeys(apiKeysResult.data)
        }

        // Fetch KYC status from student profile
        const profileResponse = await fetch('/api/student/profile')
        if (profileResponse.ok) {
          const profileData = await profileResponse.json()
          // Map database status to display status
          const statusMap: Record<string, "pending" | "approved" | "rejected" | "not_started"> = {
            'NOT_STARTED': 'not_started',
            'IN_PROGRESS': 'pending',
            'PENDING_REVIEW': 'pending',
            'APPROVED': 'approved',
            'REJECTED': 'rejected'
          }
          setKycStatus(statusMap[profileData.profile.kycStatus] || 'not_started')
        }

      } catch (err) {
        console.error('Error fetching dashboard data:', err)
        setError(err instanceof Error ? err.message : 'Failed to load dashboard data')
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  // Calculate stats from real data
  const sandboxStats = {
    totalTests: walletData?.recentTransactions.length || 0,
    successfulTests: walletData?.recentTransactions.filter(t => t.status === 'SUCCESSFUL').length || 0,
    failedTests: walletData?.recentTransactions.filter(t => t.status === 'FAILED').length || 0,
    apiCalls: apiKeys.reduce((total, key) => total + key.usageCount, 0),
  }

  // Format transactions for display
  const testTransactions = walletData?.recentTransactions.map(transaction => ({
    id: transaction.id,
    type: transaction.type === 'COLLECTION' ? 'Collection' : 'Disbursement',
    amount: `+${transaction.amount.toLocaleString()} ${transaction.currency}`,
    status: transaction.status.toLowerCase() === 'successful' ? 'success' :
            transaction.status.toLowerCase() === 'failed' ? 'failed' : 'pending',
    date: new Date(transaction.createdAt).toLocaleDateString(),
    time: new Date(transaction.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
    phone: 'N/A', // We don't store payer phone in recent transactions view
    description: `${transaction.type.toLowerCase()} transaction`,
  })) || []

  // Get primary API key info
  const primaryApiKey = apiKeys.length > 0 ? apiKeys[0] : null

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading dashboard data...</p>
        </div>
      </div>
    )
  }

  // Show error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <svg className="h-8 w-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <p className="text-gray-900 font-medium mb-2">Failed to load dashboard</p>
          <p className="text-gray-500 mb-4">{error}</p>
          <Button onClick={() => window.location.reload()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-6 md:mb-8">
        <h1 className="text-2xl md:text-3xl font-bold">Overview</h1>
        <p className="text-sm md:text-base text-gray-500">Welcome to your MTN MoMo Sandbox Dashboard</p>
      </div>

      {/* Sandbox Environment Notice */}
      <div className="mb-6 rounded-lg bg-blue-50 border border-blue-200 p-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-blue-400"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-blue-800">🧪 Sandbox Environment Active</p>
            <p className="text-sm text-blue-700 mt-1">
              All transactions are simulated. No real money is involved. Perfect for testing and learning!
            </p>
          </div>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {/* KYC Status */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">KYC Status</CardTitle>
          </CardHeader>
          <CardContent>
            {kycStatus === "approved" && (
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="bg-success text-white">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-1 h-3 w-3"
                  >
                    <polyline points="20 6 9 17 4 12" />
                  </svg>
                  Verified
                </Badge>
              </div>
            )}
            {kycStatus === "pending" && (
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="bg-yellow-500 text-white">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-1 h-3 w-3"
                  >
                    <circle cx="12" cy="12" r="10" />
                    <polyline points="12 6 12 12 16 14" />
                  </svg>
                  Pending
                </Badge>
              </div>
            )}
            {kycStatus === "rejected" && (
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="bg-destructive text-white">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-1 h-3 w-3"
                  >
                    <line x1="18" y1="6" x2="6" y2="18" />
                    <line x1="6" y1="6" x2="18" y2="18" />
                  </svg>
                  Rejected
                </Badge>
              </div>
            )}
            {kycStatus === "not_started" && (
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="bg-gray-500 text-white">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-1 h-3 w-3"
                  >
                    <circle cx="12" cy="12" r="10" />
                    <line x1="12" y1="8" x2="12" y2="12" />
                    <line x1="12" y1="16" x2="12.01" y2="16" />
                  </svg>
                  Not Started
                </Badge>
              </div>
            )}
            <p className="text-xs text-gray-500 mt-2">For production access only (sandbox bypasses KYC)</p>
          </CardContent>
        </Card>

        {/* Sandbox Wallet Balance */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Virtual Wallet</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {walletData ? walletData.balance.toLocaleString() : '0'} {walletData?.currency || 'XAF'}
            </div>
            <p className="text-xs text-gray-500 mt-2">Test funds available</p>
          </CardContent>
        </Card>

        {/* API Key Status */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">API Key</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              {primaryApiKey ? (
                <Badge variant="outline" className={primaryApiKey.isActive ? "bg-green-500 text-white" : "bg-gray-500 text-white"}>
                  {primaryApiKey.isActive ? 'Active' : 'Inactive'}
                </Badge>
              ) : (
                <Badge variant="outline" className="bg-gray-500 text-white">
                  No API Key
                </Badge>
              )}
            </div>
            <div className="text-xs text-gray-500 mt-2">
              {primaryApiKey
                ? `Created on ${new Date(primaryApiKey.createdAt).toLocaleDateString()}`
                : 'Create an API key to get started'
              }
            </div>
          </CardContent>
        </Card>

        {/* API Calls */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">API Calls</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{sandboxStats.apiCalls}</div>
            <p className="text-xs text-gray-500 mt-2">Total test requests</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-3 mt-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Tests</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold">{sandboxStats.totalTests}</div>
            <p className="text-xs text-gray-500">Transactions tested</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-success">
              {Math.round((sandboxStats.successfulTests / sandboxStats.totalTests) * 100)}%
            </div>
            <p className="text-xs text-gray-500">Test success rate</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Failed Tests</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-destructive">{sandboxStats.failedTests}</div>
            <p className="text-xs text-gray-500">Error responses</p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="mt-6">
        <CardHeader className="pb-2 md:pb-6">
          <CardTitle className="text-lg md:text-xl">Quick Actions</CardTitle>
          <CardDescription className="text-xs md:text-sm">Common testing scenarios</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 md:gap-4">
            <Button asChild className="h-auto flex-col py-2 md:py-4 text-xs md:text-sm bg-blue-600 hover:bg-blue-700">
              <Link href="/sandbox/dashboard/api-testing">
                <svg className="w-5 h-5 md:w-6 md:h-6 mb-1 md:mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 10V3L4 14h7v7l9-11h-7z"
                  />
                </svg>
                Test API
              </Link>
            </Button>
            <Button variant="outline" asChild className="h-auto flex-col py-2 md:py-4 text-xs md:text-sm">
              <Link href="/sandbox/dashboard/wallet">
                <svg className="w-5 h-5 md:w-6 md:h-6 mb-1 md:mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                Add Funds
              </Link>
            </Button>
            <Button variant="outline" asChild className="h-auto flex-col py-2 md:py-4 text-xs md:text-sm">
              <Link href="/sandbox/dashboard/api-docs">
                <svg className="w-5 h-5 md:w-6 md:h-6 mb-1 md:mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                API Docs
              </Link>
            </Button>
            <Button variant="outline" asChild className="h-auto flex-col py-2 md:py-4 text-xs md:text-sm">
              <Link href="/sandbox/dashboard/transactions">
                <svg className="w-5 h-5 md:w-6 md:h-6 mb-1 md:mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                  />
                </svg>
                Transactions
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Recent Test Transactions */}
      <Card className="mt-6">
        <CardHeader className="pb-2 md:pb-6">
          <CardTitle className="text-lg md:text-xl">Recent Test Transactions</CardTitle>
          <CardDescription className="text-xs md:text-sm">Your latest sandbox activity</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 md:space-y-4">
            {testTransactions.slice(0, 3).map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between border-b pb-2">
                <div className="flex items-center space-x-2 md:space-x-3">
                  <div
                    className={`w-2 h-2 rounded-full ${
                      transaction.status === "success" ? "bg-success" : "bg-destructive"
                    }`}
                  />
                  <div>
                    <p className="text-xs md:text-sm font-medium truncate max-w-[120px] md:max-w-none">
                      {transaction.description}
                    </p>
                    <p className="text-[10px] md:text-xs text-gray-500">
                      {transaction.date} at {transaction.time}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-xs md:text-sm font-medium">{transaction.amount}</p>
                  <p className="text-[10px] md:text-xs text-gray-500">{transaction.phone}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="outline" asChild className="w-full text-xs md:text-sm">
            <Link href="/sandbox/dashboard/transactions">View All Transactions</Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}