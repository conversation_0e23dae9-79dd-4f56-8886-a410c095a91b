"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Copy, Check } from "lucide-react"

export default function ApiAccessPage() {
  const [showFullApiKey, setShowFullApiKey] = useState(false)
  const [copied, setCopied] = useState(false)

  // Sandbox API Key
  const apiKey = "sk_test_sandbox_1234567890abcdef1234567890abcdef"
  const maskedApiKey = "sk_test_sandbox_••••••••••••••••••••••••••••1234"

  const apiKeyMetadata = {
    name: "Sandbox API Key",
    environment: "sandbox",
    createdAt: "2025-06-01",
    lastUsed: "2025-06-23",
    expiresAt: "2026-06-01",
  }

  const copyToClipboard = () => {
    navigator.clipboard.writeText(apiKey)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold">API Access</h1>
        <p className="text-gray-500">Manage your Sandbox API Keys and credentials</p>
      </div>

      {/* Sandbox Environment Notice */}
      <div className="mb-6 rounded-lg bg-blue-50 border border-blue-200 p-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <svg
              className="h-5 w-5 text-blue-400"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm font-medium text-blue-800">🧪 Sandbox API Keys</p>
            <p className="text-sm text-blue-700 mt-1">
              These keys only work in the sandbox environment. No real money will be transacted.
            </p>
          </div>
        </div>
      </div>

      {/* API Key Card */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Sandbox API Key</CardTitle>
              <CardDescription>Use this key for testing MTN MoMo integration</CardDescription>
            </div>
            <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-300">
              SANDBOX
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            {/* API Key Display */}
            <div>
              <label className="text-sm font-medium mb-1 block">API Key</label>
              <div className="flex items-center space-x-2">
                <code className="flex-1 p-3 bg-gray-100 rounded-md text-sm font-mono overflow-auto">
                  {showFullApiKey ? apiKey : maskedApiKey}
                </code>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFullApiKey(!showFullApiKey)}
                  className="whitespace-nowrap"
                >
                  {showFullApiKey ? "Hide" : "Show"}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={copyToClipboard}
                  className="min-w-[70px] flex items-center justify-center"
                >
                  {copied ? (
                    <>
                      <Check className="mr-1 h-4 w-4" /> Copied
                    </>
                  ) : (
                    <>
                      <Copy className="mr-1 h-4 w-4" /> Copy
                    </>
                  )}
                </Button>
              </div>
            </div>

            {/* Key Metadata */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
              <div className="space-y-1">
                <p className="text-sm font-medium">Key Name</p>
                <p className="text-sm text-gray-500">{apiKeyMetadata.name}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Environment</p>
                <div className="text-sm text-gray-500">
                  <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-300">
                    {apiKeyMetadata.environment.toUpperCase()}
                  </Badge>
                </div>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Created At</p>
                <p className="text-sm text-gray-500">{apiKeyMetadata.createdAt}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Last Used</p>
                <p className="text-sm text-gray-500">{apiKeyMetadata.lastUsed}</p>
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium">Expires</p>
                <p className="text-sm text-gray-500">{apiKeyMetadata.expiresAt}</p>
              </div>
            </div>
          </div>

          {/* Security Guidelines */}
          <Alert variant="destructive" className="bg-red-50 border-red-200 text-red-800">
            <AlertTitle className="text-red-800">API Key Security Guidelines</AlertTitle>
            <AlertDescription className="text-red-700">
              <ul className="list-disc pl-5 space-y-1 mt-2">
                <li>This is a test key for the sandbox environment only</li>
                <li>Never expose API keys in client-side code or public repositories</li>
                <li>Use environment variables to store API keys in your code</li>
                <li>Keep your API keys confidential and secure</li>
              </ul>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* API Base URLs */}
      <Card>
        <CardHeader>
          <CardTitle>API Base URLs</CardTitle>
          <CardDescription>Endpoints for your Sandbox integration</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <label className="text-sm font-medium mb-1 block">Base URL</label>
            <div className="flex items-center space-x-2">
              <code className="flex-1 p-3 bg-gray-100 rounded-md text-sm font-mono">
                https://sandbox-api.starterpay.cm/v1
              </code>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  navigator.clipboard.writeText("https://sandbox-api.starterpay.cm/v1")
                  setCopied(true)
                  setTimeout(() => setCopied(false), 2000)
                }}
              >
                Copy
              </Button>
            </div>
          </div>

          <div className="space-y-3 mt-4">
            <div>
              <p className="text-sm font-medium mb-1">Collections API</p>
              <code className="block p-2 bg-gray-100 rounded-md text-sm font-mono">
                POST /collections/request-to-pay
              </code>
              <code className="block p-2 bg-gray-100 rounded-md text-sm font-mono mt-2">
                GET /collections/{"{transactionId}"}/status
              </code>
            </div>

            <div className="mt-2">
              <p className="text-sm font-medium mb-1">Disbursements API</p>
              <code className="block p-2 bg-gray-100 rounded-md text-sm font-mono">
                POST /disbursements/transfer
              </code>
              <code className="block p-2 bg-gray-100 rounded-md text-sm font-mono mt-2">
                GET /disbursements/{"{transactionId}"}/status
              </code>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
            <div className="flex items-start gap-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-blue-600 mt-0.5"
              >
                <circle cx="12" cy="12" r="10" />
                <path d="M12 16v-4" />
                <path d="M12 8h.01" />
              </svg>
              <div>
                <p className="text-sm font-medium text-blue-800">Need more help?</p>
                <p className="text-xs text-blue-700 mt-1">
                  Visit the API Testing page to try these endpoints with a user-friendly interface.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}