/**
 * Sandbox Service
 * Manages sandbox wallets, API keys, and transactions
 * Integrates with MTN MoMo service
 */

import { prisma } from './prisma'
import { MTNMoMoService, MoMoUser, RequestToPayRequest, MoMoError } from './mtn-momo'
import { SandboxTransactionStatus, SandboxTransactionType } from '@prisma/client'
import { v4 as uuidv4 } from 'uuid'
import bcrypt from 'bcryptjs'

export interface CreateApiKeyRequest {
  userId: string
  keyName: string
}

export interface CreateApiKeyResponse {
  id: string
  keyName: string
  apiKey: string // Only returned once
  environment: string
  rateLimit: number
}

export interface CollectPaymentRequest {
  amount: number
  currency?: string
  externalId: string
  payerPhone: string
  payerMessage?: string
  payeeNote?: string
  callbackUrl?: string
}

export interface CollectPaymentResponse {
  transactionId: string
  status: SandboxTransactionStatus
  amount: number
  currency: string
  externalId: string
  payerPhone: string
  createdAt: Date
}

export class SandboxError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 400
  ) {
    super(message)
    this.name = 'SandboxError'
  }
}

export class SandboxService {
  private momoService: MTNMoMoService
  private systemMoMoUser: MoMoUser | null = null

  constructor() {
    // Initialize MTN MoMo service with environment variables
    this.momoService = new MTNMoMoService({
      subscriptionKey: process.env.MTN_MOMO_SUBSCRIPTION_KEY!,
      environment: 'sandbox'
    })
  }

  /**
   * Initialize or get system MoMo user for API calls
   */
  private async getSystemMoMoUser(): Promise<MoMoUser> {
    if (this.systemMoMoUser) {
      return this.systemMoMoUser
    }

    // Check if we have stored system credentials
    const storedUserId = process.env.MTN_MOMO_USER_ID
    const storedApiKey = process.env.MTN_MOMO_API_KEY

    if (storedUserId && storedApiKey) {
      this.systemMoMoUser = {
        userId: storedUserId,
        apiKey: storedApiKey
      }
      return this.systemMoMoUser
    }

    // Create new system user
    console.log('Creating new MTN MoMo system user...')
    try {
      this.systemMoMoUser = await this.momoService.setupApiUser()

      console.log('✅ MTN MoMo System User Created Successfully:')
      console.log('User ID:', this.systemMoMoUser.userId)
      console.log('API Key:', this.systemMoMoUser.apiKey)
      console.log('🔧 Add these to your .env file:')
      console.log(`MTN_MOMO_USER_ID=${this.systemMoMoUser.userId}`)
      console.log(`MTN_MOMO_API_KEY=${this.systemMoMoUser.apiKey}`)
    } catch (error) {
      console.error('❌ Failed to create MTN MoMo system user:', error)
      throw new SandboxError(
        'Failed to initialize MTN MoMo system user. Please check your subscription key.',
        'MOMO_INIT_FAILED',
        500
      )
    }

    return this.systemMoMoUser
  }

  /**
   * Initialize sandbox wallet for user
   */
  async initializeWallet(userId: string): Promise<void> {
    const existingWallet = await prisma.sandboxWallet.findUnique({
      where: { userId }
    })

    if (!existingWallet) {
      await prisma.sandboxWallet.create({
        data: {
          userId,
          balance: 100000, // Default 100,000 XAF
          currency: 'XAF'
        }
      })
    }
  }

  /**
   * Get user's sandbox wallet
   */
  async getWallet(userId: string) {
    await this.initializeWallet(userId)
    
    return await prisma.sandboxWallet.findUnique({
      where: { userId },
      include: {
        transactions: {
          orderBy: { createdAt: 'desc' },
          take: 10
        }
      }
    })
  }



  /**
   * Create API key for user
   */
  async createApiKey(request: CreateApiKeyRequest): Promise<CreateApiKeyResponse> {
    try {
      console.log('SandboxService.createApiKey called with:', request)

      // First create the database record to get the ID
      console.log('Creating API key in database...')
      const sandboxApiKey = await prisma.sandboxApiKey.create({
        data: {
          userId: request.userId,
          keyName: request.keyName,
          keyHash: 'temp', // Temporary value, will be updated
          environment: 'sandbox'
        }
      })
      console.log('API key created in database:', { id: sandboxApiKey.id, keyName: sandboxApiKey.keyName })

      // Generate API key using the database ID
      const apiKey = `sk_sandbox_${sandboxApiKey.id}`
      console.log('Generated API key (first 20 chars):', apiKey.substring(0, 20) + '...')

      // Hash the API key and update the record
      const hashedKey = await bcrypt.hash(apiKey, 10)
      console.log('API key hashed successfully')

      // Update the record with the actual hash
      await prisma.sandboxApiKey.update({
        where: { id: sandboxApiKey.id },
        data: { keyHash: hashedKey }
      })

      return {
        id: sandboxApiKey.id,
        keyName: sandboxApiKey.keyName,
        apiKey, // Only returned once
        environment: sandboxApiKey.environment,
        rateLimit: sandboxApiKey.rateLimit
      }
    } catch (error) {
      console.error('Error in SandboxService.createApiKey:', error)
      throw error
    }
  }

  /**
   * Validate API key and get user
   */
  async validateApiKey(apiKey: string): Promise<{ userId: string; keyId: string } | null> {
    const apiKeys = await prisma.sandboxApiKey.findMany({
      where: { isActive: true }
    })

    for (const key of apiKeys) {
      const isValid = await bcrypt.compare(apiKey, key.keyHash)
      if (isValid) {
        // Update usage
        await prisma.sandboxApiKey.update({
          where: { id: key.id },
          data: {
            usageCount: { increment: 1 },
            lastUsedAt: new Date()
          }
        })

        return {
          userId: key.userId,
          keyId: key.id
        }
      }
    }

    return null
  }

  /**
   * Collect payment (main function)
   */
  async collectPayment(
    request: CollectPaymentRequest,
    userId: string,
    apiKeyId?: string
  ): Promise<CollectPaymentResponse> {
    try {
      // Validate phone number format
      if (!this.isValidPhoneNumber(request.payerPhone)) {
        throw new SandboxError(
          'Invalid phone number format. Use international format (e.g., 237XXXXXXXX)',
          'INVALID_PHONE_NUMBER'
        )
      }

      // Create transaction record
      const mtnReferenceId = uuidv4()
      const transaction = await prisma.sandboxTransaction.create({
        data: {
          userId,
          apiKeyId,
          externalId: request.externalId,
          amount: request.amount,
          currency: request.currency || 'XAF',
          type: SandboxTransactionType.COLLECTION,
          status: SandboxTransactionStatus.PENDING,
          mtnReferenceId,
          payerPhone: request.payerPhone,
          payerMessage: request.payerMessage,
          payeeNote: request.payeeNote,
          callbackUrl: request.callbackUrl
        }
      })

      // Get system MoMo user
      const momoUser = await this.getSystemMoMoUser()

      // Prepare MTN MoMo request
      const momoRequest: RequestToPayRequest = {
        amount: request.amount.toString(),
        currency: request.currency || 'EUR', // EUR required for sandbox
        externalId: request.externalId,
        payer: {
          partyIdType: 'MSISDN',
          partyId: request.payerPhone
        },
        payerMessage: request.payerMessage,
        payeeNote: request.payeeNote
      }

      // Call MTN MoMo API
      const mtnTransactionId = await this.momoService.requestToPay(momoRequest, momoUser)

      // Update transaction with MTN transaction ID
      await prisma.sandboxTransaction.update({
        where: { id: transaction.id },
        data: { mtnTransactionId }
      })

      // Start background status checking
      this.checkTransactionStatusLater(transaction.id, mtnTransactionId, momoUser)

      return {
        transactionId: transaction.id,
        status: SandboxTransactionStatus.PENDING,
        amount: request.amount,
        currency: request.currency || 'XAF',
        externalId: request.externalId,
        payerPhone: request.payerPhone,
        createdAt: transaction.createdAt
      }

    } catch (error) {
      if (error instanceof MoMoError) {
        throw new SandboxError(
          `MTN MoMo error: ${error.message}`,
          error.code,
          error.statusCode || 500
        )
      }
      throw error
    }
  }

  /**
   * Get transaction details
   */
  async getTransaction(transactionId: string, userId: string) {
    const transaction = await prisma.sandboxTransaction.findFirst({
      where: {
        id: transactionId,
        userId
      }
    })

    if (!transaction) {
      throw new SandboxError('Transaction not found', 'TRANSACTION_NOT_FOUND', 404)
    }

    return transaction
  }

  /**
   * List user transactions
   */
  async listTransactions(userId: string, limit: number = 20) {
    return await prisma.sandboxTransaction.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit
    })
  }

  /**
   * Check transaction status with MTN MoMo (background task)
   */
  private async checkTransactionStatusLater(
    transactionId: string,
    mtnTransactionId: string,
    momoUser: MoMoUser
  ): Promise<void> {
    // Wait a bit before checking (simulate processing time)
    setTimeout(async () => {
      try {
        const status = await this.momoService.getTransactionStatus(mtnTransactionId, momoUser)
        
        await prisma.sandboxTransaction.update({
          where: { id: transactionId },
          data: {
            status: status.status as SandboxTransactionStatus,
            completedAt: status.status !== 'PENDING' ? new Date() : null,
            failureReason: status.status === 'FAILED' ? 'Payment failed' : null
          }
        })

        // Update wallet balance if successful
        if (status.status === 'SUCCESSFUL') {
          const transaction = await prisma.sandboxTransaction.findUnique({
            where: { id: transactionId }
          })
          
          if (transaction) {
            await prisma.sandboxWallet.update({
              where: { userId: transaction.userId },
              data: {
                balance: { increment: transaction.amount }
              }
            })
          }
        }

      } catch (error) {
        console.error('Error checking transaction status:', error)
        
        // Mark as failed if we can't check status
        await prisma.sandboxTransaction.update({
          where: { id: transactionId },
          data: {
            status: SandboxTransactionStatus.FAILED,
            failureReason: 'Status check failed',
            completedAt: new Date()
          }
        })
      }
    }, 5000) // Check after 5 seconds
  }

  /**
   * Validate phone number format
   */
  private isValidPhoneNumber(phone: string): boolean {
    // Basic validation for Cameroon numbers (237XXXXXXXX)
    const phoneRegex = /^237[0-9]{8}$/
    return phoneRegex.test(phone)
  }
}
