"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Menu } from "lucide-react"
import {
  SidebarProvider,
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarTrigger,
  SidebarInset,
} from "@/components/ui/sidebar"
import {
  Home,
  Key,
  Wallet,
  Receipt,
  FlaskConical,
  FileText,
  LogOut,
} from "lucide-react"
import { useIsMobile } from "@/hooks/use-mobile"

export default function SandboxLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const pathname = usePathname()
  const isMobile = useIsMobile()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  const sidebarItems = [
    {
      title: "Overview",
      href: "/sandbox/dashboard",
      icon: <Home className="h-4 w-4" />,
    },
    {
      title: "API Access",
      href: "/sandbox/dashboard/api-access",
      icon: <Key className="h-4 w-4" />,
    },
    {
      title: "Transactions",
      href: "/sandbox/dashboard/transactions",
      icon: <Receipt className="h-4 w-4" />,
    },
    {
      title: "Wallet",
      href: "/sandbox/dashboard/wallet",
      icon: <Wallet className="h-4 w-4" />,
    },
    {
      title: "API Testing",
      href: "/sandbox/dashboard/api-testing",
      icon: <FlaskConical className="h-4 w-4" />,
    },
    {
      title: "API Docs",
      href: "/sandbox/dashboard/api-docs",
      icon: <FileText className="h-4 w-4" />,
    },
  ]

  // Mobile navigation component
  const MobileNav = () => (
    <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="right" className="w-[300px] sm:w-[400px] p-0">
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-between p-4 border-b">
            <div className="flex items-center space-x-2">
              <div className="relative flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-br from-blue-600 to-blue-700 text-white">
                <span className="absolute -right-1 -top-1 flex h-3 w-3 items-center justify-center rounded-full bg-yellow-400 text-[8px] font-bold text-black">
                  🧪
                </span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-4 w-4"
                >
                  <path d="M19 5c-1.5 0-2.8 1.4-3 2-3.5-1.5-11-.3-11 5 0 1.8 0 3 2 4.5V20h4v-2h3v2h4v-4c1-.5 1.7-1 2-2h2v-7h-2c0-1-.5-1.5-1-2z" />
                  <path d="M2 9v1c0 1.1.9 2 2 2h1" />
                  <path d="M16 4h2a1 1 0 0 1 1 1" />
                </svg>
              </div>
              <span className="text-sm font-bold">
                Sandbox <span className="text-blue-600">Dashboard</span>
              </span>
            </div>
          </div>
          <div className="p-4 flex-1 overflow-auto">
            <Badge variant="outline" className="w-full mb-3 bg-blue-100 text-blue-700 border-blue-300 justify-center">
              🧪 Testing Environment
            </Badge>
            <nav className="flex flex-col gap-2 mt-4">
              {sidebarItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`flex items-center gap-2 p-2 rounded-md text-sm ${
                    pathname === item.href
                      ? "bg-blue-100 text-blue-700 font-medium"
                      : "hover:bg-gray-100"
                  }`}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  {item.icon}
                  <span>{item.title}</span>
                </Link>
              ))}
              <Link
                href="/sandbox-info"
                className="flex items-center gap-2 p-2 rounded-md text-sm hover:bg-gray-100 mt-2"
                onClick={() => setMobileMenuOpen(false)}
              >
                <LogOut className="h-4 w-4" />
                <span>Exit Sandbox</span>
              </Link>
            </nav>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )

  return (
    <SidebarProvider defaultOpen={true}>
      <div className="flex min-h-screen">
        <Sidebar>
          <SidebarHeader className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="relative flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-br from-blue-600 to-blue-700 text-white">
                <span className="absolute -right-1 -top-1 flex h-3 w-3 items-center justify-center rounded-full bg-yellow-400 text-[8px] font-bold text-black">
                  🧪
                </span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-4 w-4"
                >
                  <path d="M19 5c-1.5 0-2.8 1.4-3 2-3.5-1.5-11-.3-11 5 0 1.8 0 3 2 4.5V20h4v-2h3v2h4v-4c1-.5 1.7-1 2-2h2v-7h-2c0-1-.5-1.5-1-2z" />
                  <path d="M2 9v1c0 1.1.9 2 2 2h1" />
                  <path d="M16 4h2a1 1 0 0 1 1 1" />
                </svg>
              </div>
              <span className="text-sm font-bold">
                Sandbox <span className="text-blue-600">Dashboard</span>
              </span>
            </div>
            <SidebarTrigger />
          </SidebarHeader>
          <SidebarContent className="p-2">
            <Badge variant="outline" className="w-full mb-3 bg-blue-100 text-blue-700 border-blue-300 justify-center">
              🧪 Testing Environment
            </Badge>
            <SidebarMenu>
              {sidebarItems.map((item) => (
                <SidebarMenuItem key={item.href}>
                  <SidebarMenuButton asChild isActive={pathname === item.href}>
                    <Link href={item.href}>
                      {item.icon}
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
              <SidebarMenuItem>
                <SidebarMenuButton asChild>
                  <Link href="/sandbox">
                    <LogOut className="h-4 w-4" />
                    <span>Exit Sandbox</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarContent>
        </Sidebar>
        <SidebarInset>
          <div className="flex min-h-screen flex-col">
            <header className="border-b bg-white">
              <div className="container flex h-16 items-center justify-between px-4">
                <div className="flex items-center gap-4">
                  <span className="text-lg md:text-xl font-bold">StarterPay Sandbox</span>
                </div>
                <div className="flex items-center gap-2 md:gap-4">
                  {/* Mobile menu hamburger button */}
                  {isMobile && <MobileNav />}
                  <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-300 text-xs md:text-sm">
                    🧪 Test Mode
                  </Badge>
                </div>
              </div>
            </header>
            <main className="flex-1 p-4 md:p-6">
              <div className="container px-0 md:px-4">
                {children}
              </div>
            </main>
          </div>
        </SidebarInset>
      </div>
    </SidebarProvider>
  )
}