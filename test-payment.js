/**
 * Test script to verify the MTN MoMo payment collection fix
 */

const API_BASE = 'http://localhost:3001';

// Test data
const testPayment = {
  amount: 1000,
  currency: 'XAF',
  externalId: 'test-' + Date.now(),
  payerPhone: '237123456789',
  payerMessage: 'Test payment',
  payeeNote: 'Testing MTN MoMo integration'
};

async function testPaymentCollection() {
  console.log('🧪 Testing MTN MoMo Payment Collection...');
  console.log('Test data:', testPayment);
  
  try {
    // First, let's get a valid API key (assuming we have one)
    // For now, we'll use a dummy key to see the error handling
    const response = await fetch(`${API_BASE}/api/sandbox/collect-payment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-api-key-123'
      },
      body: JSON.stringify(testPayment)
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const responseData = await response.json();
    console.log('Response data:', responseData);
    
    if (response.ok) {
      console.log('✅ Payment collection test successful!');
    } else {
      console.log('❌ Payment collection test failed');
      console.log('Error details:', responseData);
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testPaymentCollection();
